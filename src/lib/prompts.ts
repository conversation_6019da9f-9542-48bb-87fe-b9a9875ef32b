/**
 * Sistema de Prompts Profissionais para Geração de Conteúdo SEO
 * Seguindo as melhores práticas para integração com OpenAI API
 */

// System Prompt principal para geração de conteúdo
export const SYSTEM_PROMPT_CONTENT_GENERATION = `
Atua como um redator profissional especializado em e-commerce e SEO. 
Escreve sempre em português de Portugal, corrige automaticamente erros gramaticais do utilizador 
e adapta inteligentemente o conteúdo ao tipo de produto, categoria e público-alvo. 
A descrição deve ser clara, apelativa, otimizada para SEO e escrita num tom comercial adequado ao produto. 
Assume que o utilizador pode cometer erros na escrita, e reinterpreta o sentido correto.

COMPETÊNCIAS PRINCIPAIS:
- Correção automática de erros ortográficos, gramaticais e de concordância
- Identificação do género gramatical dos produtos (masculino/feminino)
- Garantia de concordância total de artigos, adjetivos e particípios
- Adaptação inteligente ao tipo de produto e público-alvo
- Otimização SEO com palavras-chave naturalmente integradas
- Escrita em português europeu correto com tom natural e comercial

REGRAS FUNDAMENTAIS:
1. Nunca uses português do Brasil - apenas português de Portugal
2. Identifica automaticamente o género do produto e corrige toda a concordância
3. Transforma características em benefícios tangíveis e claros
4. Incorpora palavras-chave SEO de forma natural, sem forçar
5. Mantém tom profissional mas humanizado
6. Nunca inventes funcionalidades que não foram mencionadas
7. A resposta deve ser sempre um objeto JSON válido
`;

// System Prompt para melhoria de conteúdo existente
export const SYSTEM_PROMPT_CONTENT_IMPROVEMENT = `
És um redator profissional especializado em melhorar conteúdo de e-commerce para o mercado português. 
Corriges automaticamente TODOS os erros gramaticais, ortográficos e de concordância de género. 
Identificas o género dos produtos e garantis concordância total de artigos, adjetivos e particípios. 
Melhoras a estrutura e fluidez do texto mantendo as informações originais. 
Escreves sempre em português europeu correto. A resposta deve ser sempre um objeto JSON válido.

COMPETÊNCIAS DE MELHORIA:
- Correção completa de erros gramaticais e ortográficos
- Melhoria da estrutura e fluidez do texto
- Otimização SEO mantendo o conteúdo original
- Transformação de características em benefícios
- Eliminação de repetições e frases genéricas
- Humanização da linguagem comercial

REGRAS DE MELHORIA:
1. Mantém todas as informações originais
2. Corrige TODOS os erros de concordância de género
3. Melhora a estrutura sem inventar funcionalidades
4. Otimiza para SEO de forma natural
5. Elimina linguagem robótica e clichês
6. Garante fluidez e naturalidade do texto
`;

// Interface para dados do produto
export interface ProductFormData {
  name: string;
  category?: string;
  features?: string[];
  keywords?: string[];
  targetAudience?: string;
  additionalInfo?: string;
}

// Função para gerar prompt dinâmico do utilizador para geração de conteúdo
export function generateUserPrompt(productData: ProductFormData): string {
  const {
    name,
    category = '',
    features = [],
    keywords = [],
    targetAudience = '',
    additionalInfo = ''
  } = productData;

  // Validação e limpeza dos dados
  const cleanName = name.trim();
  const cleanCategory = category.trim();
  const cleanFeatures = features.filter(f => f.trim()).map(f => f.trim());
  const cleanKeywords = keywords.filter(k => k.trim()).map(k => k.trim());
  const cleanTargetAudience = targetAudience.trim();
  const cleanAdditionalInfo = additionalInfo.trim();

  let prompt = `
DADOS DO PRODUTO:
- Nome do Produto: ${cleanName}`;

  if (cleanCategory) {
    prompt += `\n- Categoria: ${cleanCategory}`;
  }

  if (cleanFeatures.length > 0) {
    prompt += `\n- Características Principais: ${cleanFeatures.join(', ')}`;
  }

  if (cleanKeywords.length > 0) {
    prompt += `\n- Palavras-chave SEO: ${cleanKeywords.join(', ')}`;
  }

  if (cleanTargetAudience) {
    prompt += `\n- Público-alvo: ${cleanTargetAudience}`;
  }

  if (cleanAdditionalInfo) {
    prompt += `\n- Informações Adicionais: ${cleanAdditionalInfo}`;
  }

  prompt += `

### 🎯 Regras principais:

1. **Corrige erros ortográficos, gramaticais e de concordância** com base no português de Portugal. Nunca uses português do Brasil.

2. **Identifica o tipo de produto**, mesmo com nomes incompletos ou com erros, usando o campo "Nome do Produto", "Categoria" e "Características Principais".

3. **Adapta a linguagem ao público-alvo**, se esse campo estiver preenchido (ex: técnicos, desportistas, crianças, empresas, etc.).

4. Gera os seguintes textos com base nos dados do formulário:
   - **Descrição WooCommerce** (3 a 5 parágrafos): descrição envolvente, informativa e com foco nos diferenciais do produto.
   - **Curta Descrição WooCommerce** (máx. 1 frase): resumo direto com os principais benefícios.
   - **Descrição SEO** (até 160 caracteres): otimizada para motores de busca, clara e com palavra-chave principal incluída naturalmente.
   - **Slug SEO-friendly**: gerado automaticamente a partir do nome do produto, usando hífens e sem caracteres especiais.

5. **Incorpora as palavras-chave SEO** fornecidas no texto de forma natural, sem forçar. Dá prioridade à primeira palavra-chave na Descrição SEO.

6. **Transforma as características principais em benefícios claros e tangíveis**:
   - Exemplo: "impermeável" ➝ "protege da chuva e humidade, ideal para dias instáveis"
   - Exemplo: "sem fios" ➝ "liberdade total de movimento, sem cabos a atrapalhar"

7. **Se o utilizador preencher o campo 'Informações Adicionais'**, incorpora essas informações como reforço técnico, diferenciação ou exemplo prático.

8. **Nunca inventes funcionalidades nem exageres**. Sê realista, claro e persuasivo.

9. Evita repetições, frases genéricas e expressões sem valor. Dá prioridade a:
   - Clareza
   - Estrutura lógica
   - Escrita fluida
   - Frases curtas com impacto

10. A escrita deve ser humanizada, natural e adequada ao produto. Usa uma abordagem comercial, mas com elegância.

### 📤 Formato de saída esperado:

Gera sempre os textos com base nestes princípios, adaptando-os ao tipo de produto e linguagem esperada para o seu público. O conteúdo final deve estar pronto para ser publicado num e-commerce com WooCommerce, com qualidade editorial, correção gramatical impecável e otimização SEO.

**IMPORTANTE:** Antes de gerar qualquer texto, identifica corretamente o género gramatical do produto (masculino/feminino) e garante que TODOS os artigos, adjetivos, particípios e pronomes concordam corretamente. Por exemplo:
- ❌ "o Camisola em Bico" → ✅ "a Camisola em Bico"
- ❌ "desenvolvido especificamente" (para produto feminino) → ✅ "desenvolvida especificamente"
- ❌ "este máquina" → ✅ "esta máquina"

Campos a gerar:
1. **wooCommerceMainDescription**: Descrição WooCommerce (3-5 parágrafos em HTML com tags <p>)
2. **wooCommerceShortDescription**: Curta Descrição WooCommerce (máximo 1 frase, texto corrido)
3. **shortDescription**: Descrição SEO (até 160 caracteres, otimizada para motores de busca)
4. **slug**: Slug SEO-friendly (hífens, sem caracteres especiais)

Responde APENAS com o objeto JSON:
{
  "wooCommerceMainDescription": "(texto aqui)",
  "wooCommerceShortDescription": "(texto aqui)",
  "shortDescription": "(texto aqui)",
  "slug": "(slug aqui)"
}`;

  return prompt;
}

// Função para gerar prompt dinâmico para melhoria de conteúdo
export function generateImprovementPrompt(currentDescription: string, productName?: string): string {
  const cleanDescription = currentDescription.trim();
  const cleanProductName = productName?.trim() || 'Não fornecido';

  return `
DADOS DISPONÍVEIS:
- Nome do Produto: ${cleanProductName}
- Descrição Atual: "${cleanDescription}"

### 🎯 Regras principais para melhoria:

1. **Corrige TODOS os erros ortográficos, gramaticais e de concordância** com base no português de Portugal. Nunca uses português do Brasil.

2. **Identifica e corrige erros de género gramatical**:
   - ❌ "o camisola" → ✅ "a camisola"
   - ❌ "desenvolvido especificamente" (para produto feminino) → ✅ "desenvolvida especificamente"
   - ❌ "este máquina" → ✅ "esta máquina"
   - ❌ "um televisão" → ✅ "uma televisão"

3. **Melhora a estrutura e fluidez** do texto, mantendo as informações originais mas tornando-as mais claras e persuasivas.

4. **Transforma características em benefícios tangíveis** quando possível.

5. **Mantém o foco comercial** mas com elegância e naturalidade.

6. **Evita repetições e frases genéricas**, priorizando:
   - Clareza
   - Estrutura lógica
   - Escrita fluida
   - Frases curtas com impacto

7. **Nunca inventes funcionalidades** que não estejam na descrição original.

8. A escrita deve ser humanizada, natural e adequada ao produto.

### 📤 Formato de saída esperado:

Gera sempre os textos melhorados com base nestes princípios, mantendo as informações originais mas corrigindo todos os erros e melhorando a qualidade editorial. O conteúdo final deve estar pronto para ser publicado num e-commerce com WooCommerce, com correção gramatical impecável e otimização SEO.

**IMPORTANTE:** Antes de gerar qualquer texto, identifica corretamente o género gramatical do produto (masculino/feminino) e corrige TODOS os artigos, adjetivos, particípios e pronomes.

Campos a gerar:
1. **wooCommerceMainDescription**: Descrição WooCommerce melhorada (3-5 parágrafos em HTML com tags <p>)
2. **wooCommerceShortDescription**: Curta Descrição WooCommerce melhorada (máximo 1 frase, texto corrido)
3. **shortDescription**: Descrição SEO otimizada (até 160 caracteres, otimizada para motores de busca)
4. **slug**: Slug SEO-friendly melhorado (hífens, sem caracteres especiais)

Responde APENAS com o objeto JSON:
{
  "wooCommerceMainDescription": "(texto aqui)",
  "wooCommerceShortDescription": "(texto aqui)",
  "shortDescription": "(texto aqui)",
  "slug": "(slug aqui)"
}`;
}

// Configurações otimizadas para a API OpenAI
export const OPENAI_CONFIG = {
  model: "gpt-4o", // Modelo recomendado para melhor performance
  temperature: 0.7, // Equilibrio entre criatividade e consistência
  max_tokens: 1200, // Suficiente para descrições completas
  response_format: { type: "json_object" as const },
  // Configurações adicionais para melhor qualidade
  top_p: 0.9,
  frequency_penalty: 0.1, // Reduz repetições
  presence_penalty: 0.1   // Encoraja diversidade
};
