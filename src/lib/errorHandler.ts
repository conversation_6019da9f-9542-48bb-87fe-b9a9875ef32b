/**
 * Sistema Avançado de Tratamento de Erros e Retry
 * Para APIs OpenAI com estratégias inteligentes de recuperação
 */

// Tipos de erro específicos da OpenAI
export interface OpenAIError {
  status?: number;
  message?: string;
  type?: string;
  code?: string;
}

// Configuração de retry
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableStatuses: number[];
}

// Configuração padrão de retry
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 segundo
  maxDelay: 10000, // 10 segundos
  backoffMultiplier: 2,
  retryableStatuses: [429, 500, 502, 503, 504] // Rate limit, server errors
};

/**
 * Classe para tratamento avançado de erros da OpenAI
 */
export class OpenAIErrorHandler {
  private config: RetryConfig;

  constructor(config: RetryConfig = DEFAULT_RETRY_CONFIG) {
    this.config = config;
  }

  /**
   * Executa uma função com retry inteligente
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: string = 'OpenAI API call'
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 0; attempt <= this.config.maxRetries; attempt++) {
      try {
        const result = await operation();
        
        // Log sucesso após retry
        if (attempt > 0) {
          console.log(`✅ ${context} bem-sucedido após ${attempt} tentativas`);
        }
        
        return result;
      } catch (error) {
        lastError = error;
        
        // Verificar se deve tentar novamente
        if (!this.shouldRetry(error, attempt)) {
          break;
        }
        
        // Calcular delay para próxima tentativa
        const delay = this.calculateDelay(attempt);
        
        console.warn(
          `⚠️ ${context} falhou (tentativa ${attempt + 1}/${this.config.maxRetries + 1}). ` +
          `Erro: ${this.getErrorMessage(error)}. ` +
          `Tentando novamente em ${delay}ms...`
        );
        
        // Aguardar antes da próxima tentativa
        await this.sleep(delay);
      }
    }
    
    // Se chegou aqui, todas as tentativas falharam
    console.error(`❌ ${context} falhou após ${this.config.maxRetries + 1} tentativas`);
    throw this.enhanceError(lastError, context);
  }

  /**
   * Determina se deve tentar novamente baseado no erro
   */
  private shouldRetry(error: any, attempt: number): boolean {
    // Não retry se excedeu o máximo de tentativas
    if (attempt >= this.config.maxRetries) {
      return false;
    }

    // Verificar status HTTP
    const status = this.getErrorStatus(error);
    if (status && this.config.retryableStatuses.includes(status)) {
      return true;
    }

    // Verificar tipos específicos de erro
    const errorType = this.getErrorType(error);
    const retryableTypes = ['rate_limit_exceeded', 'server_error', 'timeout'];
    
    return retryableTypes.includes(errorType);
  }

  /**
   * Calcula delay com backoff exponencial
   */
  private calculateDelay(attempt: number): number {
    const delay = this.config.baseDelay * Math.pow(this.config.backoffMultiplier, attempt);
    
    // Adicionar jitter para evitar thundering herd
    const jitter = Math.random() * 0.1 * delay;
    
    return Math.min(delay + jitter, this.config.maxDelay);
  }

  /**
   * Extrai status do erro
   */
  private getErrorStatus(error: any): number | null {
    if (error?.status) return error.status;
    if (error?.response?.status) return error.response.status;
    if (error?.code === 'ECONNRESET') return 503;
    if (error?.code === 'ETIMEDOUT') return 504;
    return null;
  }

  /**
   * Extrai tipo do erro
   */
  private getErrorType(error: any): string {
    if (error?.type) return error.type;
    if (error?.code === 'rate_limit_exceeded') return 'rate_limit_exceeded';
    if (error?.message?.includes('timeout')) return 'timeout';
    
    const status = this.getErrorStatus(error);
    if (status && status >= 500) return 'server_error';
    
    return 'unknown';
  }

  /**
   * Extrai mensagem do erro
   */
  private getErrorMessage(error: any): string {
    if (error?.message) return error.message;
    if (error?.error?.message) return error.error.message;
    return 'Erro desconhecido';
  }

  /**
   * Melhora o erro com informações contextuais
   */
  private enhanceError(error: any, context: string): Error {
    const status = this.getErrorStatus(error);
    const message = this.getErrorMessage(error);
    const type = this.getErrorType(error);

    let enhancedMessage = `${context} falhou: ${message}`;
    
    // Adicionar sugestões baseadas no tipo de erro
    switch (status) {
      case 401:
        enhancedMessage += '\n💡 Sugestão: Verifique se a chave da API OpenAI está correta e ativa.';
        break;
      case 429:
        enhancedMessage += '\n💡 Sugestão: Quota da API excedida. Verifique o seu plano ou aguarde antes de tentar novamente.';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        enhancedMessage += '\n💡 Sugestão: Erro do servidor OpenAI. Tente novamente em alguns minutos.';
        break;
      case 400:
        enhancedMessage += '\n💡 Sugestão: Verifique os parâmetros da requisição.';
        break;
    }

    const enhancedError = new Error(enhancedMessage);
    enhancedError.name = `OpenAIError_${status || type}`;
    
    // Preservar propriedades originais
    Object.assign(enhancedError, { originalError: error, status, type });
    
    return enhancedError;
  }

  /**
   * Utilitário para sleep
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Instância global do error handler
 */
export const openaiErrorHandler = new OpenAIErrorHandler();

/**
 * Função utilitária para classificar erros da OpenAI
 */
export function classifyOpenAIError(error: any): {
  category: 'auth' | 'quota' | 'server' | 'client' | 'network' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userMessage: string;
  technicalMessage: string;
  shouldRetry: boolean;
} {
  const status = error?.status || error?.response?.status;
  const message = error?.message || error?.error?.message || 'Erro desconhecido';

  switch (status) {
    case 401:
      return {
        category: 'auth',
        severity: 'critical',
        userMessage: 'Erro de autenticação. Por favor, contacte o suporte.',
        technicalMessage: 'Chave da API OpenAI inválida ou expirada',
        shouldRetry: false
      };

    case 429:
      return {
        category: 'quota',
        severity: 'high',
        userMessage: 'Serviço temporariamente indisponível devido a alta demanda. Tente novamente em alguns minutos.',
        technicalMessage: 'Quota da API OpenAI excedida',
        shouldRetry: true
      };

    case 400:
      return {
        category: 'client',
        severity: 'medium',
        userMessage: 'Erro na requisição. Por favor, tente novamente.',
        technicalMessage: `Parâmetros inválidos: ${message}`,
        shouldRetry: false
      };

    case 500:
    case 502:
    case 503:
    case 504:
      return {
        category: 'server',
        severity: 'high',
        userMessage: 'Erro temporário do servidor. Tente novamente em alguns minutos.',
        technicalMessage: `Erro do servidor OpenAI: ${status}`,
        shouldRetry: true
      };

    default:
      if (message.includes('timeout') || message.includes('ETIMEDOUT')) {
        return {
          category: 'network',
          severity: 'medium',
          userMessage: 'Timeout na conexão. Tente novamente.',
          technicalMessage: 'Timeout na requisição à API',
          shouldRetry: true
        };
      }

      return {
        category: 'unknown',
        severity: 'medium',
        userMessage: 'Erro inesperado. Tente novamente ou contacte o suporte.',
        technicalMessage: message,
        shouldRetry: false
      };
  }
}

/**
 * Função para gerar mensagens de erro amigáveis para o utilizador
 */
export function generateUserFriendlyErrorMessage(error: any): string {
  const classification = classifyOpenAIError(error);
  
  let message = classification.userMessage;
  
  // Adicionar informações de contexto se apropriado
  if (classification.category === 'quota') {
    message += '\n\nEste erro é temporário e resolve-se automaticamente. O sistema irá tentar novamente.';
  } else if (classification.category === 'server') {
    message += '\n\nEste é um problema temporário do servidor. O sistema irá tentar novamente automaticamente.';
  } else if (classification.category === 'auth') {
    message += '\n\nPor favor, contacte o administrador do sistema.';
  }
  
  return message;
}

/**
 * Middleware para logging estruturado de erros
 */
export function logError(error: any, context: string, additionalInfo?: any): void {
  const classification = classifyOpenAIError(error);
  
  const logData = {
    timestamp: new Date().toISOString(),
    context,
    category: classification.category,
    severity: classification.severity,
    technicalMessage: classification.technicalMessage,
    originalError: error?.message || error,
    additionalInfo
  };
  
  // Log baseado na severidade
  switch (classification.severity) {
    case 'critical':
      console.error('🚨 ERRO CRÍTICO:', logData);
      break;
    case 'high':
      console.error('🔴 ERRO ALTO:', logData);
      break;
    case 'medium':
      console.warn('🟡 ERRO MÉDIO:', logData);
      break;
    case 'low':
      console.info('🟢 ERRO BAIXO:', logData);
      break;
  }
}
