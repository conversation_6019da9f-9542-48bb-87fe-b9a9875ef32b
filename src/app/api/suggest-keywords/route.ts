import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const apiKey = process.env.OPENAI_API_KEY;
const mockMode = process.env.MOCK_OPENAI === 'true';

let openai: OpenAI | null = null;
if (apiKey && !mockMode) {
  openai = new OpenAI({ apiKey });
}

// Enhanced mock function for sophisticated keyword suggestions
function generateMockKeywords(productName: string, productCategory?: string): string[] {
  const cleanProductName = productName.toLowerCase().trim();
  const cleanCategory = productCategory?.toLowerCase().trim() || '';

  // Generate diverse, SEO-optimized keywords
  const keywordTemplates = [
    // Commercial intent keywords
    `comprar ${cleanProductName} online`,
    `${cleanProductName} preço portugal`,
    `${cleanProductName} melhor preço`,
    `onde comprar ${cleanProductName}`,

    // Informational keywords
    `${cleanProductName} características`,
    `${cleanProductName} avaliações`,
    `melhor ${cleanProductName}`,
    `${cleanProductName} qualidade`,

    // Category-specific keywords
    ...(cleanCategory ? [
      `${cleanCategory} ${cleanProductName}`,
      `${cleanProductName} ${cleanCategory} premium`,
      `melhor ${cleanCategory} portugal`
    ] : []),

    // Long-tail keywords
    `${cleanProductName} entrega rápida`,
    `${cleanProductName} garantia`,
    `${cleanProductName} promoção`,

    // Local SEO keywords
    `${cleanProductName} lisboa`,
    `${cleanProductName} porto`,
    `${cleanProductName} portugal`
  ];

  // Filter and select the most relevant keywords
  const relevantKeywords = keywordTemplates
    .filter(keyword => keyword.length <= 50) // Keep reasonable length
    .filter(keyword => !keyword.includes('undefined'))
    .slice(0, 8); // Get more options

  // Randomly select 3 diverse keywords
  const selectedKeywords = [];
  const usedTypes = new Set();

  // Ensure we get different types of keywords
  const keywordTypes = [
    { pattern: /comprar|preço|onde/, type: 'commercial' },
    { pattern: /melhor|qualidade|características/, type: 'informational' },
    { pattern: /lisboa|porto|portugal/, type: 'local' }
  ];

  for (const keyword of relevantKeywords) {
    if (selectedKeywords.length >= 3) break;

    const keywordType = keywordTypes.find(type => type.pattern.test(keyword))?.type || 'general';

    if (!usedTypes.has(keywordType) || selectedKeywords.length < 2) {
      selectedKeywords.push(keyword);
      usedTypes.add(keywordType);
    }
  }

  // Fill remaining slots if needed
  while (selectedKeywords.length < 3 && relevantKeywords.length > selectedKeywords.length) {
    const remaining = relevantKeywords.filter(k => !selectedKeywords.includes(k));
    if (remaining.length > 0) {
      selectedKeywords.push(remaining[0]);
    } else {
      break;
    }
  }

  return selectedKeywords.slice(0, 3);
}

export async function POST(request: Request) {
  try {
    const { productName, productCategory } = await request.json();

    if (!productName) {
      return NextResponse.json({ error: 'O nome do produto é obrigatório.' }, { status: 400 });
    }

    // Use mock mode if enabled
    if (mockMode) {
      const keywords = generateMockKeywords(productName, productCategory);
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate delay
      return NextResponse.json({ keywords });
    }

    if (!openai) {
      return NextResponse.json(
        { error: 'A API da OpenAI não está configurada ou ative o modo mock.' },
        { status: 500 }
      );
    }

    const prompt = `
      Para um produto com o nome "${productName}"${productCategory ? ` na categoria "${productCategory}"` : ''}, sugira 3 a 5 palavras-chave de cauda longa (long-tail keywords) para SEO, focadas no mercado português.

      As palavras-chave devem ser específicas e relevantes para quem procura ativamente este tipo de produto online.

      Responda APENAS com um objeto JSON que contém uma única chave "keywords", que é um array de strings.

      Exemplo de resposta:
      {
        "keywords": [
          "sapatilhas de corrida leves para homem",
          "melhores sapatilhas para maratona",
          "calçado de corrida com bom amortecimento"
        ]
      }
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "És um especialista em SEO para e-commerce em Portugal. A tua tarefa é sugerir palavras-chave de cauda longa (long-tail) relevantes para produtos específicos. Escreves sempre em português de Portugal. A resposta deve ser sempre um objeto JSON com um array de strings."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 200,
      response_format: { type: "json_object" },
      top_p: 0.9,
      frequency_penalty: 0.1,
      presence_penalty: 0.1
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error("A resposta da API está vazia.");
    }

    const parsedContent = JSON.parse(content);
    return NextResponse.json(parsedContent);

  } catch (error) {
    console.error('Erro na API de sugestão de palavras-chave:', error);
    return NextResponse.json({ error: 'Falha ao sugerir palavras-chave.' }, { status: 500 });
  }
}
